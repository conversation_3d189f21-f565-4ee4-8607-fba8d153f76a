<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>EaseAI Widget - 密码保护演示</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 20px;
        background-color: #f5f5f5;
      }

      .container {
        max-width: 800px;
        margin: 0 auto;
        background: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      h1 {
        color: #333;
        text-align: center;
        margin-bottom: 30px;
      }

      .section {
        margin-bottom: 30px;
      }

      .section h2 {
        color: #666;
        border-bottom: 2px solid #eee;
        padding-bottom: 10px;
      }

      .code-block {
        background: #f8f8f8;
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 15px;
        margin: 15px 0;
        overflow-x: auto;
      }

      .code-block code {
        font-family: 'Courier New', monospace;
        font-size: 14px;
      }

      .button {
        background: #007cba;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        margin: 5px;
      }

      .button:hover {
        background: #005a87;
      }

      .status {
        padding: 10px;
        border-radius: 5px;
        margin: 10px 0;
      }

      .status.success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }

      .status.error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }

      .highlight {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        padding: 15px;
        border-radius: 5px;
        margin: 15px 0;
      }

      .highlight strong {
        color: #856404;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>EaseAI Widget - 密码保护演示</h1>

      <div class="highlight">
        <strong>注意：</strong
        >此演示展示了如何使用带密码保护的对外发布。请确保您有正确的对外发布ID和密码。
      </div>

      <div class="section">
        <h2>Widget状态</h2>
        <div id="status" class="status error">Widget未加载</div>
      </div>

      <div class="section">
        <h2>密码保护配置</h2>
        <p>当对外发布设置了访问密码时，需要在script标签中添加<code>data-password</code>属性：</p>
        <div class="code-block">
          <code>
            &lt;script src="../dist-widget/easeai-widget.js" data-agent-id="your-agent-id"
            data-client-id="your-client-id" data-password="your-password" data-title="智能客服助手"
            data-api-endpoint="http://demo.easeidea.com/prod-api"&gt; &lt;/script&gt;
          </code>
        </div>
      </div>

      <div class="section">
        <h2>API调用流程</h2>
        <p>Widget会按以下顺序调用API：</p>
        <ol>
          <li>
            <strong>获取对外发布详情</strong>：<code>GET /open/outward/detail/{agentId}</code>
          </li>
          <li>
            <strong>获取临时token</strong>：<code>POST /auth/getToken/{agentId}</code>（包含密码）
          </li>
          <li>
            <strong>创建游客会话</strong>：<code
              >POST /biz/guest/conversation/apply?id={agentId}</code
            >
          </li>
          <li><strong>发送消息</strong>：<code>POST /biz/guest/conversation/completions</code></li>
        </ol>
      </div>

      <div class="section">
        <h2>错误处理</h2>
        <p>Widget会自动处理以下错误情况：</p>
        <ul>
          <li>对外发布不存在或已禁用</li>
          <li>密码错误或缺失</li>
          <li>网络连接问题</li>
          <li>API服务异常</li>
        </ul>
      </div>

      <div class="section">
        <h2>测试功能</h2>
        <button class="button" onclick="testWidget()">测试Widget</button>
        <button class="button" onclick="checkStatus()">检查状态</button>
        <button class="button" onclick="showConfig()">显示配置</button>
      </div>

      <div class="section">
        <h2>配置信息</h2>
        <div id="config-info" class="code-block">
          <code>配置信息将在点击"显示配置"后显示</code>
        </div>
      </div>
    </div>

    <!-- 加载 EaseAI Widget - 请替换为您的实际配置 -->
    <script
      src="../dist-widget/easeai-widget.js"
      data-agent-id="1950835089345712129"
      data-client-id="e5cd7e4891bf95d1d19206ce24a7b32e"
      data-password="1111"
      data-title="智能客服助手"
      data-subtitle="我是您的专属AI助手，随时为您提供帮助"
      data-welcome-message="您好！有什么可以帮助您的吗？"
      data-input-placeholder="请输入您的问题..."
      data-position="bottom-right"
      data-theme="light"
      data-primary-color="#4c5cec"
      data-api-endpoint="http://demo.easeidea.com/prod-api"
      data-static-url="http://demo.easeidea.com"
    ></script>

    <script>
      // 检查Widget状态
      function checkStatus() {
        const statusEl = document.getElementById('status')
        if (window.EaseAI) {
          statusEl.className = 'status success'
          statusEl.textContent = 'Widget已成功加载'
        } else {
          statusEl.className = 'status error'
          statusEl.textContent = 'Widget未加载'
        }
      }

      // 测试Widget功能
      function testWidget() {
        if (window.EaseAI) {
          console.log('Widget已加载！请查看页面右下角的聊天图标。')
          // 可以调用Widget的方法
          window.EaseAI.show()
        } else {
          console.log('Widget未加载，请检查配置。')
        }
      }

      // 显示配置信息
      function showConfig() {
        const configEl = document.getElementById('config-info')
        const script = document.querySelector('script[src*="easeai-widget"]')

        if (script) {
          const config = {
            agentId: script.getAttribute('data-agent-id'),
            clientId: script.getAttribute('data-client-id'),
            password: script.getAttribute('data-password') ? '***' : '(未设置)',
            title: script.getAttribute('data-title'),
            subtitle: script.getAttribute('data-subtitle'),
            welcomeMessage: script.getAttribute('data-welcome-message'),
            inputPlaceholder: script.getAttribute('data-input-placeholder'),
            position: script.getAttribute('data-position'),
            theme: script.getAttribute('data-theme'),
            primaryColor: script.getAttribute('data-primary-color'),
            apiEndpoint: script.getAttribute('data-api-endpoint'),
          }

          configEl.innerHTML = `<code>${JSON.stringify(config, null, 2)}</code>`
        }
      }

      // 页面加载完成后检查状态
      window.addEventListener('load', () => {
        setTimeout(checkStatus, 1000)
      })
    </script>
  </body>
</html>
