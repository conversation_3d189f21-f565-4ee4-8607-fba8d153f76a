<template>
  <div class="test-container">
    <h1>客服部件测试页面</h1>
    
    <div class="config-panel">
      <h2>配置面板</h2>
      
      <div class="form-group">
        <label>对外发布ID:</label>
        <input v-model="config.agentId" type="text" placeholder="输入对外发布ID" />
      </div>
      
      <div class="form-group">
        <label>标题:</label>
        <input v-model="config.title" type="text" placeholder="客服标题" />
      </div>
      
      <div class="form-group">
        <label>副标题:</label>
        <input v-model="config.subtitle" type="text" placeholder="客服副标题" />
      </div>
      
      <div class="form-group">
        <label>头像URL:</label>
        <input v-model="config.avatar" type="text" placeholder="头像URL" />
      </div>
      
      <div class="form-group">
        <label>欢迎消息:</label>
        <textarea v-model="config.welcomeMessage" placeholder="欢迎消息"></textarea>
      </div>
      
      <div class="form-group">
        <label>输入占位符:</label>
        <input v-model="config.inputPlaceholder" type="text" placeholder="输入占位符" />
      </div>
      
      <div class="form-group">
        <label>位置:</label>
        <select v-model="config.position">
          <option value="bottom-right">右下角</option>
          <option value="bottom-left">左下角</option>
        </select>
      </div>
      
      <div class="form-group">
        <label>主题:</label>
        <select v-model="config.theme">
          <option value="light">浅色</option>
          <option value="dark">深色</option>
        </select>
      </div>
      
      <div class="form-group">
        <label>主色调:</label>
        <input v-model="config.primaryColor" type="color" />
      </div>
      
      <div class="form-group">
        <label>API端点:</label>
        <input v-model="config.apiEndpoint" type="text" placeholder="API端点" />
      </div>
      
      <div class="form-group">
        <label>访问密码:</label>
        <input v-model="config.password" type="password" placeholder="访问密码（可选）" />
      </div>
      
      <div class="form-actions">
        <button @click="resetConfig">重置配置</button>
        <button @click="testWidget">测试部件</button>
      </div>
    </div>
    
    <div class="status-panel">
      <h2>状态信息</h2>
      <div class="status-item">
        <strong>配置状态:</strong> {{ configStatus }}
      </div>
      <div class="status-item">
        <strong>当前配置:</strong>
        <pre>{{ JSON.stringify(config, null, 2) }}</pre>
      </div>
    </div>
    
    <!-- 客服部件 -->
    <Widget :config="config" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import Widget from './Widget.vue'

// 配置状态
const config = ref({
  agentId: '',
  title: '智能客服助手',
  subtitle: '我是您的专属AI助手，随时为您提供帮助',
  avatar: '',
  welcomeMessage: '您好！有什么可以帮助您的吗？',
  inputPlaceholder: '请输入您的问题...',
  position: 'bottom-right' as 'bottom-right' | 'bottom-left',
  theme: 'light' as 'light' | 'dark',
  primaryColor: '#4c5cec',
  apiEndpoint: '/prod-api',
  password: '',
})

// 配置状态
const configStatus = computed(() => {
  if (!config.value.agentId) {
    return '❌ 缺少对外发布ID'
  }
  return '✅ 配置完整'
})

// 重置配置
const resetConfig = () => {
  config.value = {
    agentId: '',
    title: '智能客服助手',
    subtitle: '我是您的专属AI助手，随时为您提供帮助',
    avatar: '',
    welcomeMessage: '您好！有什么可以帮助您的吗？',
    inputPlaceholder: '请输入您的问题...',
    position: 'bottom-right',
    theme: 'light',
    primaryColor: '#4c5cec',
    apiEndpoint: '/prod-api',
    password: '',
  }
}

// 测试部件
const testWidget = () => {
  if (!config.value.agentId) {
    alert('请先输入对外发布ID')
    return
  }
  
  console.log('测试客服部件配置:', config.value)
  alert('部件配置已更新，请查看右下角的客服部件')
}

// 监听配置变化
watch(config, (newConfig) => {
  console.log('配置已更新:', newConfig)
}, { deep: true })
</script>

<style scoped>
.test-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

h1, h2 {
  color: #333;
  margin-bottom: 20px;
}

.config-panel {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 600;
  color: #555;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #4c5cec;
}

.form-group textarea {
  height: 80px;
  resize: vertical;
}

.form-actions {
  display: flex;
  gap: 10px;
  margin-top: 30px;
}

.form-actions button {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.form-actions button:first-child {
  background: #6c757d;
  color: white;
}

.form-actions button:first-child:hover {
  background: #5a6268;
}

.form-actions button:last-child {
  background: #4c5cec;
  color: white;
}

.form-actions button:last-child:hover {
  background: #3a4bc8;
}

.status-panel {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #4c5cec;
  margin-bottom: 30px;
}

.status-item {
  margin-bottom: 15px;
}

.status-item:last-child {
  margin-bottom: 0;
}

.status-item pre {
  background: #2d3748;
  color: #e2e8f0;
  padding: 15px;
  border-radius: 6px;
  overflow-x: auto;
  font-size: 12px;
  margin-top: 10px;
}
</style>
