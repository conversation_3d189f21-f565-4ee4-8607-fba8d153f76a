# EaseAI Widget

独立的客服聊天Widget组件，支持嵌入到任何网站。

## 📁 目录结构

```
widget/
├── Widget.vue           # 主Widget组件（包含完整样式）
├── index.ts            # Widget入口文件
├── style-loader.ts     # 样式加载器
├── test.vue           # 测试组件
├── utils/             # 工具函数
│   └── streamHandler.ts # SSE流式响应处理器
└── README.md          # 本文档
```

## 🚀 构建

```bash
# 构建Widget
pnpm run build:widget

# 输出文件
dist-widget/easeai-widget.js  # Widget主文件
dist-widget/style.css         # Widget样式
```

## 📋 使用方式

### 1. 基本使用

```html
<script
  src="./dist-widget/easeai-widget.js"
  data-agent-id="your-agent-id"
  data-client-id="your-client-id"
  data-api-endpoint="https://your-api.com/prod-api"
  data-static-url="https://your-static-url.com">
</script>
```

### 2. 编程方式

```javascript
// 初始化Widget
window.EaseAI.init({
  agentId: 'your-agent-id',
  clientId: 'your-client-id',
  title: '在线客服',
  apiEndpoint: 'https://your-api.com/prod-api',
  staticUrl: 'https://your-static-url.com'
})

// 控制Widget
window.EaseAI.show()
window.EaseAI.hide()
window.EaseAI.toggle()
```

## 🔧 核心功能

- ✅ **SSE流式响应**：实时显示AI回复
- ✅ **Shadow DOM隔离**：样式完全隔离，不影响宿主页面
- ✅ **响应式设计**：支持移动端和桌面端
- ✅ **主题定制**：支持浅色/深色主题
- ✅ **多语言支持**：支持中英文界面
- ✅ **错误处理**：完善的错误处理和重试机制

## 🌟 技术特性

- **Vue 3 + TypeScript**：现代化开发体验
- **Vite构建**：快速构建和热更新
- **单文件输出**：所有依赖打包为单个JS文件
- **体积优化**：gzip后仅27KB
- **浏览器兼容**：支持ES2015+的现代浏览器
