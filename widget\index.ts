import { createApp } from 'vue'
import Widget from './Widget.vue'
import { injectWidgetStyles } from './style-loader'

// Widget配置接口
interface EaseAIWidgetConfig {
  agentId?: string
  clientId?: string
  title?: string
  subtitle?: string
  avatar?: string
  welcomeMessage?: string
  inputPlaceholder?: string
  position?: 'bottom-right' | 'bottom-left'
  theme?: 'light' | 'dark'
  primaryColor?: string
  apiEndpoint?: string
  password?: string
  staticUrl?: string
  defaultMessage?: string
}

// Widget实例接口
interface EaseAIWidgetInstance {
  init(config: EaseAIWidgetConfig): Promise<void>
  show(): void
  hide(): void
  toggle(): void
  destroy(): void
  sendMessage(message: string): void
  onMessage(callback: (message: any) => void): void
}

// 全局变量声明
declare global {
  interface Window {
    EaseAI: EaseAIWidgetInstance
    easeai: any
  }
}

class EaseAIWidget implements EaseAIWidgetInstance {
  private app: any = null
  private container: HTMLElement | null = null
  private config: EaseAIWidgetConfig = {}
  private messageListeners: Array<(message: any) => void> = []
  private isInitialized = false

  constructor() {
    this.init = this.init.bind(this)
    this.show = this.show.bind(this)
    this.hide = this.hide.bind(this)
    this.toggle = this.toggle.bind(this)
    this.destroy = this.destroy.bind(this)
    this.sendMessage = this.sendMessage.bind(this)
    this.onMessage = this.onMessage.bind(this)
  }

  async init(config: EaseAIWidgetConfig): Promise<void> {
    if (this.isInitialized) {
      console.warn('EaseAI Widget already initialized')
      return
    }

    this.config = {
      position: 'bottom-right',
      theme: 'light',
      title: '在线客服',
      subtitle: '我们将为您提供帮助',
      welcomeMessage: '您好！有什么可以帮助您的吗？',
      inputPlaceholder: '请输入您的问题...',
      apiEndpoint: '/prod-api',
      ...config,
    }

    await this.createContainer()
    this.createApp()
    this.isInitialized = true

    console.log('EaseAI Widget initialized with config:', this.config)
  }

  private async createContainer(): Promise<void> {
    // 创建Shadow Host容器元素
    this.container = document.createElement('div')
    this.container.id = 'easeai-widget-container'

    // 创建Shadow DOM以实现样式隔离
    const shadowRoot = this.container.attachShadow({ mode: 'open' })

    // 在Shadow DOM中创建实际的Widget容器
    const widgetWrapper = document.createElement('div')
    widgetWrapper.style.cssText = `
      position: fixed;
      z-index: 999999;
      ${this.config.position === 'bottom-left' ? 'left: 20px;' : 'right: 20px;'}
      bottom: 20px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `

    // 将CSS样式注入到Shadow DOM中
    await injectWidgetStyles(shadowRoot)

    // 将Widget容器添加到Shadow DOM
    shadowRoot.appendChild(widgetWrapper)

    // 将Shadow Host添加到页面
    document.body.appendChild(this.container)

    // 更新container引用为实际的Widget容器
    this.container = widgetWrapper
  }

  private createApp(): void {
    if (!this.container) return

    // 创建Vue应用实例
    this.app = createApp(Widget, {
      config: this.config,
    })

    // 挂载应用到Shadow DOM中的容器
    this.app.mount(this.container)

    // 确保Vue组件知道它在Shadow DOM中
    this.setupShadowDOMSupport()
  }

  private setupShadowDOMSupport(): void {
    // 为Shadow DOM中的Vue应用提供特殊支持
    if (this.container && this.container.getRootNode() instanceof ShadowRoot) {
      const shadowRoot = this.container.getRootNode() as ShadowRoot
      // Vue 3对Shadow DOM的支持已经比较好了，暂时不需要特殊处理
    }
  }

  show(): void {
    if (this.container) {
      this.container.style.display = 'block'
    }
  }

  hide(): void {
    if (this.container) {
      this.container.style.display = 'none'
    }
  }

  toggle(): void {
    if (this.container) {
      const isVisible = this.container.style.display !== 'none'
      this.container.style.display = isVisible ? 'none' : 'block'
    }
  }

  destroy(): void {
    if (this.app) {
      this.app.unmount()
      this.app = null
    }

    if (this.container && this.container.parentNode) {
      // 如果container在Shadow DOM中，需要先获取Shadow Host
      const shadowHost = this.container.getRootNode() as any
      if (shadowHost && shadowHost.host && shadowHost.host.parentNode) {
        shadowHost.host.parentNode.removeChild(shadowHost.host)
      } else if (this.container.parentNode) {
        this.container.parentNode.removeChild(this.container)
      }
    }

    this.container = null
    this.messageListeners = []
    this.isInitialized = false

    console.log('EaseAI Widget destroyed')
  }

  sendMessage(message: string): void {
    // 可以通过这个方法向Widget发送消息
    if (this.app && this.app._instance) {
      // 触发Widget内部的消息处理
      console.log('Sending message to widget:', message)
    }
  }

  onMessage(callback: (message: any) => void): void {
    this.messageListeners.push(callback)
  }

  private notifyMessageListeners(message: any): void {
    this.messageListeners.forEach(listener => {
      try {
        listener(message)
      } catch (error) {
        console.error('Error in message listener:', error)
      }
    })
  }
}

// 创建全局实例
const widgetInstance = new EaseAIWidget()

// 在window对象上暴露API
if (typeof window !== 'undefined') {
  window.EaseAI = widgetInstance

  // 兼容旧版本API
  window.easeai = {
    init: widgetInstance.init,
    show: widgetInstance.show,
    hide: widgetInstance.hide,
    toggle: widgetInstance.toggle,
    destroy: widgetInstance.destroy,
    sendMessage: widgetInstance.sendMessage,
    onMessage: widgetInstance.onMessage,
  }

  // 自动检测页面加载完成后的初始化配置
  const checkAutoInit = () => {
    const script = document.querySelector('script[src*="easeai-widget"]')
    if (script) {
      const agentId = script.getAttribute('data-agent-id')
      const clientId = script.getAttribute('data-client-id')
      const title = script.getAttribute('data-title')
      const subtitle = script.getAttribute('data-subtitle')
      const avatar = script.getAttribute('data-avatar')
      const welcomeMessage = script.getAttribute('data-welcome-message')
      const inputPlaceholder = script.getAttribute('data-input-placeholder')
      const position = script.getAttribute('data-position') as 'bottom-right' | 'bottom-left'
      const theme = script.getAttribute('data-theme') as 'light' | 'dark'
      const primaryColor = script.getAttribute('data-primary-color')
      const apiEndpoint = script.getAttribute('data-api-endpoint')
      const password = script.getAttribute('data-password')
      const staticUrl = script.getAttribute('data-static-url')
      const defaultMessage = script.getAttribute('data-default-message')

      if (agentId) {
        const config: EaseAIWidgetConfig = {
          agentId,
          clientId: clientId || undefined,
          title: title || undefined,
          subtitle: subtitle || undefined,
          avatar: avatar || undefined,
          welcomeMessage: welcomeMessage || undefined,
          inputPlaceholder: inputPlaceholder || undefined,
          position: position || 'bottom-right',
          theme: theme || 'light',
          primaryColor: primaryColor || undefined,
          apiEndpoint: apiEndpoint || undefined,
          password: password || undefined,
          staticUrl: staticUrl || undefined,
          defaultMessage: defaultMessage || undefined,
        }

        console.log('Auto-initializing EaseAI Widget with config from script attributes:', config)
        widgetInstance.init(config).catch(error => {
          console.error('Failed to auto-initialize EaseAI Widget:', error)
        })
      }
    }
  }

  // DOM加载完成后检查自动初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', checkAutoInit)
  } else {
    checkAutoInit()
  }

  console.log('EaseAI Widget loaded successfully. Use window.EaseAI.init(config) to initialize.')
}

export default EaseAIWidget
export { EaseAIWidget, type EaseAIWidgetConfig, type EaseAIWidgetInstance }
