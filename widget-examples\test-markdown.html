<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>EaseAI Widget - Markdown 渲染测试</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 20px;
        background-color: #f5f5f5;
      }

      .container {
        max-width: 800px;
        margin: 0 auto;
        background: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      h1 {
        color: #333;
        text-align: center;
        margin-bottom: 30px;
      }

      .section {
        margin-bottom: 30px;
      }

      .section h2 {
        color: #666;
        border-bottom: 2px solid #eee;
        padding-bottom: 10px;
      }

      .button {
        background: #4c5cec;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        margin: 5px;
      }

      .button:hover {
        background: #3a4bc8;
      }

      .test-area {
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 20px;
        margin: 15px 0;
        background: #f9f9f9;
      }

      .highlight {
        background: #e7f3ff;
        border: 1px solid #b3d9ff;
        padding: 15px;
        border-radius: 5px;
        margin: 15px 0;
      }

      .highlight strong {
        color: #0066cc;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>EaseAI Widget - Markdown 渲染测试</h1>

      <div class="highlight">
        <strong>测试说明：</strong>
        此页面用于测试 Widget 的 Markdown 渲染功能，包括代码高亮、数学公式、表格等。
      </div>

      <div class="section">
        <h2>测试功能</h2>
        <button class="button" onclick="testMarkdown()">测试 Markdown 渲染</button>
        <button class="button" onclick="testCodeHighlight()">测试代码高亮</button>
        <button class="button" onclick="testMath()">测试数学公式</button>
        <button class="button" onclick="testTable()">测试表格</button>
        <button class="button" onclick="clearChat()">清空聊天</button>
      </div>

      <div class="section">
        <h2>测试结果</h2>
        <div class="test-area">
          <p>点击上方按钮开始测试，Widget 将显示相应的 Markdown 内容。</p>
          <p>请观察：</p>
          <ul>
            <li>代码块是否正确高亮</li>
            <li>数学公式是否正确渲染</li>
            <li>表格是否正确显示</li>
            <li>复制按钮是否正常工作</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- 加载 EaseAI Widget -->
    <script
      src="../dist-widget/easeai-widget.js"
      data-agent-id="1950835089345712129"
      data-client-id="e5cd7e4891bf95d1d19206ce24a7b32e"
      data-password="1111"
      data-title="Markdown 测试助手"
      data-subtitle="测试 Markdown 渲染功能"
      data-welcome-message="您好！我是 Markdown 测试助手，点击页面上的按钮来测试不同的渲染功能。"
      data-input-placeholder="输入消息测试..."
      data-position="bottom-right"
      data-theme="light"
      data-primary-color="#4c5cec"
      data-api-endpoint="http://demo.easeidea.com/prod-api"
      data-static-url="http://demo.easeidea.com"
    ></script>

    <script>
      // 测试 Markdown 基础功能
      function testMarkdown() {
        if (window.EaseAI) {
          // 模拟添加一条包含 Markdown 的消息
          const markdownContent = `# 标题测试

这是一个 **粗体** 和 *斜体* 的测试。

## 列表测试

- 项目 1
- 项目 2
  - 子项目 2.1
  - 子项目 2.2

## 链接测试

这是一个 [链接](https://www.example.com) 的测试。

> 这是一个引用块的测试。
> 可以包含多行内容。

---

这是一个分隔线上方的内容。`

          // 直接调用 Widget 的内部方法（仅用于测试）
          console.log('测试 Markdown 内容：', markdownContent)
          window.EaseAI.show()
        } else {
          alert('Widget 未加载')
        }
      }

      // 测试代码高亮
      function testCodeHighlight() {
        if (window.EaseAI) {
          const codeContent = `# 代码高亮测试

## JavaScript 代码

\`\`\`javascript
function hello(name) {
  console.log(\`Hello, \${name}!\`)
  return \`Welcome to EaseAI Widget!\`
}

const user = 'Developer'
hello(user)
\`\`\`

## Python 代码

\`\`\`python
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

# 计算前10个斐波那契数
for i in range(10):
    print(f"F({i}) = {fibonacci(i)}")
\`\`\`

## 行内代码

这是一个行内代码示例：\`const x = 42\``

          console.log('测试代码高亮：', codeContent)
          window.EaseAI.show()
        } else {
          alert('Widget 未加载')
        }
      }

      // 测试数学公式
      function testMath() {
        if (window.EaseAI) {
          const mathContent = `# 数学公式测试

## 行内公式

这是一个行内公式：$E = mc^2$

## 块级公式

$$
\\frac{d}{dx}\\left( \\int_{a}^{x} f(u)\\,du\\right) = f(x)
$$

## 复杂公式

$$
\\sum_{i=1}^{n} i = \\frac{n(n+1)}{2}
$$

$$
\\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi}
$$`

          console.log('测试数学公式：', mathContent)
          window.EaseAI.show()
        } else {
          alert('Widget 未加载')
        }
      }

      // 测试表格
      function testTable() {
        if (window.EaseAI) {
          const tableContent = `# 表格测试

## 基础表格

| 功能 | 状态 | 说明 |
|------|------|------|
| Markdown 渲染 | ✅ | 支持基础 Markdown 语法 |
| 代码高亮 | ✅ | 支持多种编程语言 |
| 数学公式 | ✅ | 支持 KaTeX 渲染 |
| 表格显示 | ✅ | 支持表格格式化 |

## 对齐表格

| 左对齐 | 居中对齐 | 右对齐 |
|:-------|:--------:|-------:|
| 内容1  |   内容2   |   内容3 |
| 较长的内容 | 中等内容 | 短内容 |`

          console.log('测试表格：', tableContent)
          window.EaseAI.show()
        } else {
          alert('Widget 未加载')
        }
      }

      // 清空聊天（如果 Widget 支持）
      function clearChat() {
        if (window.EaseAI) {
          console.log('尝试清空聊天记录')
          // 这里可以调用 Widget 的清空方法（如果有的话）
          window.EaseAI.show()
        } else {
          alert('Widget 未加载')
        }
      }

      // 页面加载完成后的初始化
      window.addEventListener('load', () => {
        setTimeout(() => {
          if (window.EaseAI) {
            console.log('Widget 已加载，可以开始测试')
          } else {
            console.log('Widget 未加载')
          }
        }, 1000)
      })
    </script>
  </body>
</html>
