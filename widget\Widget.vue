<script setup lang="ts">
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue'
import { type StreamCallbacks, WidgetStreamHandler } from './utils/streamHandler'
import MessageText from './components/MessageText.vue'

// Props接口定义
interface WidgetConfig {
  agentId?: string
  clientId?: string
  title?: string
  subtitle?: string
  avatar?: string
  welcomeMessage?: string
  inputPlaceholder?: string
  position?: 'bottom-right' | 'bottom-left'
  theme?: 'light' | 'dark'
  primaryColor?: string
  apiEndpoint?: string
  password?: string // 添加密码字段用于需要认证的对外发布
  staticUrl?: string // 静态资源URL前缀
}

interface ChatMessage {
  id: string
  text: string
  isUser: boolean
  timestamp: number
}

// Props
const props = defineProps<{
  config: WidgetConfig
}>()

// 响应式数据
const isExpanded = ref(false)
const inputText = ref('')
const isSending = ref(false)
const isTyping = ref(false)
const unreadCount = ref(0)
const messages = ref<ChatMessage[]>([])
const chatContentRef = ref<HTMLElement>()

// resolved 添加助手会话相关状态
const agentInfo = ref<any>(null)
const conversationId = ref<string>('')
const agentUserToken = ref<string>('')
const isInitialized = ref(false)
const initializationError = ref<string>('')
const outwardInfo = ref<any>(null)

// 处理静态资源URL的函数
function getStaticUrl(url?: string): string {
  if (!url) return ''

  // 如果URL已经是完整的HTTP/HTTPS URL，直接返回
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url
  }

  // 如果配置了staticUrl前缀，则拼接
  if (props.config.staticUrl) {
    const baseUrl = props.config.staticUrl.endsWith('/')
      ? props.config.staticUrl.slice(0, -1)
      : props.config.staticUrl
    const path = url.startsWith('/') ? url : `/${url}`
    return `${baseUrl}${path}`
  }

  // 否则返回原URL
  return url
}

// resolved HTTP请求工具函数，参考v1.1.2分支的API实现
function createHttpClient(baseURL?: string, token?: string) {
  // 如果提供了完整的baseURL，使用它；否则使用配置的apiEndpoint
  let apiEndpoint = baseURL || props.config.apiEndpoint || '/prod-api'

  // 如果apiEndpoint不是完整URL且不以http开头，则需要添加基础域名
  if (!apiEndpoint.startsWith('http') && !apiEndpoint.startsWith('//')) {
    // 对于widget使用，默认使用demo服务器
    const baseHost = 'http://demo.easeidea.com'
    apiEndpoint = apiEndpoint.startsWith('/')
      ? `${baseHost}${apiEndpoint}`
      : `${baseHost}/${apiEndpoint}`
  }

  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    Clientid: props.config.clientId || 'e5cd7e4891bf95d1d19206ce24a7b32e', // Widget客户端ID，可配置
  }

  if (token) {
    headers.Authorization = `Bearer ${token}`
  }

  return {
    async get<T>(url: string, params?: any): Promise<{ data: T; code: number }> {
      let fullUrl = `${apiEndpoint}${url}`
      if (params) {
        const searchParams = new URLSearchParams(params)
        fullUrl += `?${searchParams.toString()}`
      }

      const response = await fetch(fullUrl, {
        method: 'GET',
        headers,
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      return result
    },

    async post<T>(url: string, body?: any): Promise<{ data: T; code: number }> {
      const response = await fetch(`${apiEndpoint}${url}`, {
        method: 'POST',
        headers,
        body: body ? JSON.stringify(body) : undefined,
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      return result
    },
  }
}

// 定义API响应类型
interface TokenResponse {
  access_token: string
}

interface OutwardDetailResponse {
  agentId: number
  agentName: string
  agentEmoji?: string
  agentBackgroundColor?: string
  enabled: boolean
}

interface ConversationResponse {
  id: string
  name?: string
  agentName?: string
}

// 简化的SVG图标组件，使用内联SVG减小体积
const createSvgIcon = (iconName: string) => {
  const icons: Record<string, string> = {
    chat: `<svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z"/></svg>`,
    close: `<svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>`,
    send: `<svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/></svg>`,
  }
  return icons[iconName] || icons.chat
}

// 计算属性
const defaultAvatar = computed(() => {
  return (
    agentInfo.value?.emoji ||
    'https://ui-avatars.com/api/?name=AI&background=4c5cec&color=fff&size=40&rounded=true'
  )
})

const userAvatar = computed(() => {
  return 'https://ui-avatars.com/api/?name=User&background=28a745&color=fff&size=32&rounded=true'
})

const isMobile = computed(() => {
  return window.innerWidth <= 768
})

// resolved 获取对外发布详情和token，参考v1.1.2分支逻辑
async function initializeAgent() {
  if (!props.config.agentId) {
    initializationError.value = '缺少agentId配置'
    return false
  }

  try {
    console.log('初始化助手，agentId:', props.config.agentId)
    const httpClient = createHttpClient()

    // 第一步：获取对外发布详情
    const outwardResponse = await httpClient.get<OutwardDetailResponse>(
      `/open/outward/detail/${props.config.agentId}`,
    )
    if (outwardResponse.code !== 200 || !outwardResponse.data) {
      throw new Error('获取对外发布详情失败')
    }

    outwardInfo.value = outwardResponse.data
    console.log('对外发布详情:', outwardInfo.value)

    // 第二步：获取临时token
    const tokenResponse = await httpClient.post<TokenResponse>(
      `/auth/getToken/${props.config.agentId}`,
      {
        password: props.config.password || undefined,
      },
    )

    if (tokenResponse.code !== 200 || !tokenResponse.data?.access_token) {
      throw new Error('获取访问权限失败')
    }

    agentUserToken.value = tokenResponse.data.access_token

    // 设置助手信息
    agentInfo.value = {
      id: outwardInfo.value.agentId,
      name: outwardInfo.value.agentName || props.config.title || '智能客服助手',
      description: props.config.subtitle || '我是您的专属AI助手，随时为您提供帮助',
      emoji: outwardInfo.value.agentEmoji || props.config.avatar,
      backgroundColor: outwardInfo.value.agentBackgroundColor,
      status: outwardInfo.value.enabled ? 1 : 0,
    }

    console.log('助手信息初始化成功', agentInfo.value)
    return true
  } catch (error: any) {
    console.error('初始化失败:', error)
    initializationError.value = error?.message || '初始化失败'
    return false
  }
}

// resolved 创建游客会话，参考v1.1.2分支逻辑
async function createConversation() {
  if (!props.config.agentId || !agentUserToken.value) {
    console.error('缺少agentId或token')
    return false
  }

  try {
    console.log('创建游客会话，助手ID:', props.config.agentId)
    const httpClient = createHttpClient(undefined, agentUserToken.value)

    // 调用游客创建会话API
    const response = await httpClient.post<ConversationResponse>(
      `/biz/guest/conversation/apply?id=${props.config.agentId}`,
    )

    if (response.code !== 200 || !response.data?.id) {
      throw new Error('创建会话失败')
    }

    conversationId.value = response.data.id
    console.log('游客会话创建成功，ID:', conversationId.value)
    return true
  } catch (error: any) {
    console.error('创建会话失败:', error)
    initializationError.value = error?.message || '创建会话失败'
    return false
  }
}

// 方法
async function toggleWidget() {
  isExpanded.value = !isExpanded.value
  if (isExpanded.value) {
    unreadCount.value = 0

    // resolved 如果还没有初始化，先初始化助手和创建会话
    if (!isInitialized.value && props.config.agentId) {
      const agentInitialized = await initializeAgent()
      if (agentInitialized) {
        const conversationCreated = await createConversation()
        if (conversationCreated) {
          isInitialized.value = true
          // 显示欢迎消息
          setTimeout(() => {
            if (messages.value.length === 0) {
              const welcomeMessage: ChatMessage = {
                id: 'welcome-1',
                text:
                  props.config.welcomeMessage ||
                  agentInfo.value?.description ||
                  '您好！我是EaseAI智能客服，有什么可以帮助您的吗？',
                isUser: false,
                timestamp: Date.now(),
              }
              messages.value.push(welcomeMessage)
            }
          }, 500)
        }
      }
    }

    nextTick(() => {
      scrollToBottom()
    })
  }
}

function sendMessage() {
  if (!inputText.value.trim() || isSending.value) return

  // resolved 检查是否有有效的会话ID
  if (!conversationId.value) {
    console.error('没有有效的会话ID')
    return
  }

  const userMessage: ChatMessage = {
    id: Date.now().toString(),
    text: inputText.value,
    isUser: true,
    timestamp: Date.now(),
  }

  messages.value.push(userMessage)
  const messageText = inputText.value
  inputText.value = ''

  nextTick(() => {
    scrollToBottom()
  })

  // resolved 发送消息到服务器
  sendToServer(messageText)
}

async function sendToServer(message: string) {
  if (!conversationId.value || !agentUserToken.value) {
    console.error('缺少会话ID或token')
    return
  }

  isSending.value = true
  isTyping.value = true

  try {
    console.log('发送消息到游客会话:', message)

    // 使用游客对话API发送消息，参考src\utils\streamResponseManager.ts的sendRequest
    // 创建流式响应处理器
    const streamHandler = new WidgetStreamHandler()

    // 创建AI回复消息
    const aiMessage: ChatMessage = {
      id: `ai-${Date.now()}`,
      text: '',
      isUser: false,
      timestamp: Date.now(),
    }
    messages.value.push(aiMessage)

    // 获取消息在数组中的索引，用于直接更新
    const messageIndex = messages.value.length - 1

    // 发送请求并处理流式响应
    // 构建完整的请求URL
    let baseURL = props.config.apiEndpoint || '/prod-api'
    if (!baseURL.startsWith('http') && !baseURL.startsWith('//')) {
      const baseHost = 'http://demo.easeidea.com'
      baseURL = baseURL.startsWith('/') ? `${baseHost}${baseURL}` : `${baseHost}/${baseURL}`
    }
    const requestUrl = `${baseURL}/biz/guest/conversation/completions`
    const requestBody = {
      conversationId: conversationId.value,
      userMessage: message,
      hasImages: false, // Widget暂不支持图片
    }

    const response = await fetch(requestUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${agentUserToken.value}`,
        Clientid: props.config.clientId || 'e5cd7e4891bf95d1d19206ce24a7b32e',
      },
      body: JSON.stringify(requestBody),
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    // 处理流式响应
    const callbacks: StreamCallbacks = {
      onStart: () => {
        console.log('开始接收流式响应')
      },
      onContent: (content: string) => {
        // 实时更新AI消息内容
        console.log('Widget收到流式内容:', `${content.substring(0, 50)}...`)
        // 直接更新数组中的消息对象
        messages.value[messageIndex].text = content
        // 强制触发Vue响应式更新并滚动到底部
        nextTick(() => {
          scrollToBottom()
        })
      },
      onComplete: (finalContent: string) => {
        // 确保最终内容正确
        messages.value[messageIndex].text = finalContent || '抱歉，我暂时无法回答您的问题。'
        console.log('流式响应完成，最终内容:', finalContent)
        // 最终滚动到底部
        nextTick(() => {
          scrollToBottom()
        })
      },
      onError: (error: string) => {
        messages.value[messageIndex].text = `错误: ${error}`
        console.error('流式响应错误:', error)
        nextTick(() => {
          scrollToBottom()
        })
      },
    }

    await streamHandler.handleStream(response, callbacks)

    nextTick(() => {
      scrollToBottom()
    })

    // 如果widget是收起状态，增加未读计数
    if (!isExpanded.value) {
      unreadCount.value++
    }

    console.log('AI响应发送成功')
  } catch (error: any) {
    console.error('发送消息失败:', error)

    // 显示错误消息
    const errorMessage: ChatMessage = {
      id: `error-${Date.now()}`,
      text: '抱歉，发送消息时出现了问题，请稍后重试。',
      isUser: false,
      timestamp: Date.now(),
    }
    messages.value.push(errorMessage)
  } finally {
    isSending.value = false
    isTyping.value = false
  }
}

function scrollToBottom() {
  if (chatContentRef.value) {
    chatContentRef.value.scrollTop = chatContentRef.value.scrollHeight
  }
}

function formatTime(timestamp: number): string {
  // 简化的时间格式化
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  return date.toLocaleDateString()
}

function onInputKeydown(event: KeyboardEvent) {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    sendMessage()
  }
}

// 生命周期
onMounted(() => {
  console.log('Customer service widget mounted with config:', props.config)
})

onUnmounted(() => {
  // 清理工作
})

// 监听配置变化
watch(
  () => props.config,
  (newConfig, oldConfig) => {
    console.log('Widget config updated:', newConfig)
    // resolved 如果agentId发生变化，重置状态
    if (newConfig.agentId !== oldConfig?.agentId) {
      console.log('AgentId changed, resetting widget state')
      isInitialized.value = false
      conversationId.value = ''
      agentUserToken.value = ''
      agentInfo.value = null
      outwardInfo.value = null
      messages.value = []
      initializationError.value = ''
      isExpanded.value = false
      unreadCount.value = 0
    }
  },
  { deep: true },
)
</script>

<template>
  <div
    class="customer-service-widget"
    :class="{
      'widget-expanded': isExpanded,
      'widget-collapsed': !isExpanded,
      'widget-mobile': isMobile,
    }"
  >
    <!-- 客服触发按钮 -->
    <div v-if="!isExpanded" class="widget-trigger" @click="toggleWidget">
      <div class="trigger-icon" v-html="createSvgIcon('chat')"></div>
      <div v-if="unreadCount > 0" class="unread-badge">{{ unreadCount }}</div>
    </div>

    <!-- 客服聊天界面 -->
    <div v-if="isExpanded" class="widget-chat-container">
      <!-- 标题栏 -->
      <div class="widget-header">
        <div class="header-info">
          <img
            :src="getStaticUrl(agentInfo?.emoji || config.avatar) || defaultAvatar"
            alt="客服头像"
            class="avatar"
          />
          <div class="info">
            <div class="title">{{ agentInfo?.name || config.title || '在线客服' }}</div>
            <div class="subtitle">
              {{ agentInfo?.description || config.subtitle || '我们将为您提供帮助' }}
            </div>
          </div>
        </div>
        <div class="header-actions">
          <button class="action-btn" @click="toggleWidget">
            <span v-html="createSvgIcon('close')"></span>
          </button>
        </div>
      </div>

      <!-- 聊天内容 -->
      <div ref="chatContentRef" class="widget-chat-content">
        <div class="messages-container">
          <!-- 初始化错误提示 -->
          <div v-if="initializationError" class="error-message">
            <div class="error-text">{{ initializationError }}</div>
          </div>

          <!-- 欢迎消息 -->
          <div v-else-if="messages.length === 0 && !isTyping" class="welcome-message">
            <div class="welcome-text">
              {{
                config.welcomeMessage || agentInfo?.description || '您好！有什么可以帮助您的吗？'
              }}
            </div>
          </div>

          <!-- 聊天消息 -->
          <div
            v-for="(message, index) in messages"
            :key="index"
            class="message-item"
            :class="{ 'message-user': message.isUser, 'message-bot': !message.isUser }"
          >
            <div class="message-avatar">
              <img
                :src="
                  message.isUser
                    ? userAvatar
                    : getStaticUrl(agentInfo?.emoji || config.avatar) || defaultAvatar
                "
                :alt="message.isUser ? '用户' : '客服'"
              />
            </div>
            <div class="message-content">
              <MessageText
                :text="message.text"
                :inversion="message.isUser"
                :loading="message.isUser ? false : index === messages.length - 1 && isTyping"
                :error="false"
              />
              <div class="message-time">{{ formatTime(message.timestamp) }}</div>
            </div>
          </div>

          <!-- 正在输入指示器 -->
          <div v-if="isTyping" class="typing-indicator">
            <div class="typing-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="widget-input-area">
        <div class="input-container">
          <input
            v-model="inputText"
            type="text"
            :placeholder="config.inputPlaceholder || '请输入您的问题...'"
            class="message-input"
            :disabled="!isInitialized || !!initializationError"
            @keydown="onInputKeydown"
          />
          <button
            class="send-button"
            :disabled="!inputText.trim() || isSending || !isInitialized || !!initializationError"
            @click="sendMessage"
          >
            <span v-html="createSvgIcon('send')"></span>
          </button>
        </div>
        <!-- resolved 添加免责声明 -->
        <div class="disclaimer">内容由 AI 生成，仅供参考</div>
      </div>
    </div>
  </div>
</template>

<style lang="less">
/* Widget核心样式 - 精简版 */
.customer-service-widget {
  position: fixed;
  z-index: 999999;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  bottom: 20px;
  right: 20px;

  &.widget-expanded {
    width: 380px;
    height: 600px;
  }

  @media (max-width: 768px) {
    &.widget-expanded {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      width: 100vw;
      height: 100vh;
    }
  }
}

.widget-trigger {
  width: 60px;
  height: 60px;
  background: #4c5cec;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: transform 0.2s;
  position: relative;

  &:hover {
    transform: scale(1.05);
  }

  .trigger-icon {
    color: white;
    font-size: 24px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;

    svg {
      width: 100%;
      height: 100%;
      display: block;
    }
  }

  .unread-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ff4757;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
  }
}

.widget-chat-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.widget-header {
  background: linear-gradient(135deg, #4c5cec 0%, #5a67d8 100%);
  color: white;
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .header-info {
    display: flex;
    align-items: center;
    gap: 12px;

    .avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      object-fit: cover;
      border: 2px solid rgba(255, 255, 255, 0.3);
    }

    .info {
      .title {
        font-weight: 600;
        font-size: 16px;
        margin-bottom: 2px;
      }

      .subtitle {
        font-size: 12px;
        opacity: 0.9;
        font-weight: 400;
      }
    }
  }

  .header-actions {
    .action-btn {
      background: rgba(255, 255, 255, 0.1);
      border: none;
      color: white;
      cursor: pointer;
      padding: 8px;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: background-color 0.2s;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
      }

      svg {
        width: 20px;
        height: 20px;
        display: block;
      }
    }
  }
}

.widget-chat-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: #f8f9fa;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(76, 92, 236, 0.3);
    border-radius: 3px;
  }
}

.messages-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.welcome-message,
.error-message {
  text-align: center;
  padding: 20px;

  .welcome-text,
  .error-text {
    background: white;
    padding: 12px 16px;
    border-radius: 8px;
    display: inline-block;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .error-text {
    background: #fff5f5;
    border: 1px solid #fed7d7;
    color: #c53030;
  }
}

.message-item {
  display: flex;
  gap: 8px;

  &.message-user {
    flex-direction: row-reverse;

    .message-content {
      background: linear-gradient(135deg, #4c5cec 0%, #5a67d8 100%);
      color: white;
      margin-left: 40px;
      border-bottom-right-radius: 4px;

      .message-time {
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }

  &.message-bot {
    .message-content {
      background: white;
      margin-right: 40px;
      border: 1px solid #e9ecef;
      border-bottom-left-radius: 4px;
    }
  }

  .message-avatar {
    width: 32px;
    height: 32px;
    flex-shrink: 0;

    img {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      object-fit: cover;
      border: 2px solid rgba(76, 92, 236, 0.1);
    }
  }

  .message-content {
    max-width: 70%;
    padding: 10px 14px;
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .message-time {
      font-size: 11px;
      opacity: 0.7;
      font-weight: 400;
      margin-top: 4px;
    }
  }
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 8px;

  .typing-dots {
    display: flex;
    gap: 3px;
    padding: 8px 12px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);

    span {
      width: 6px;
      height: 6px;
      background: #ccc;
      border-radius: 50%;
      animation: typing 1.4s infinite ease-in-out;

      &:nth-child(1) {
        animation-delay: -0.32s;
      }
      &:nth-child(2) {
        animation-delay: -0.16s;
      }
    }
  }
}

@keyframes typing {
  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.widget-input-area {
  background: white;
  border-top: 1px solid #e9ecef;
  padding: 16px;

  .input-container {
    display: flex;
    gap: 10px;
    align-items: center;
    background: #f8f9fa;
    border-radius: 25px;
    padding: 4px;
    border: 2px solid transparent;
    transition: border-color 0.2s;

    &:focus-within {
      border-color: #4c5cec;
      background: white;
    }

    .message-input {
      flex: 1;
      border: none;
      background: transparent;
      padding: 10px 16px;
      font-size: 14px;
      outline: none;
      color: #333;

      &::placeholder {
        color: #999;
        font-weight: 400;
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }

    .send-button {
      width: 38px;
      height: 38px;
      background: linear-gradient(135deg, #4c5cec 0%, #5a67d8 100%);
      border: none;
      border-radius: 50%;
      color: white;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: background-color 0.2s;

      &:hover:not(:disabled) {
        background: linear-gradient(135deg, #3a4bc8 0%, #4c63d2 100%);
      }

      &:disabled {
        background: #ccc;
        cursor: not-allowed;
      }

      svg {
        width: 18px;
        height: 18px;
        display: block;
      }
    }
  }

  .disclaimer {
    text-align: center;
    font-size: 11px;
    color: #999;
    margin-top: 8px;
    padding: 0 4px;
    opacity: 0.8;
    line-height: 1.2;
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .widget-mobile .widget-chat-container {
    border-radius: 0;
  }

  .widget-mobile .message-item .message-content {
    max-width: 85%;
  }
}
</style>
