<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>EaseAI Widget - 基础演示</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 20px;
        background-color: #f5f5f5;
      }

      .container {
        max-width: 800px;
        margin: 0 auto;
        background: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      h1 {
        color: #333;
        text-align: center;
        margin-bottom: 30px;
      }

      .section {
        margin-bottom: 30px;
      }

      .section h2 {
        color: #666;
        border-bottom: 2px solid #eee;
        padding-bottom: 10px;
      }

      .code-block {
        background: #f8f8f8;
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 15px;
        margin: 15px 0;
        overflow-x: auto;
      }

      .code-block code {
        font-family: 'Courier New', monospace;
        font-size: 14px;
      }

      .button {
        background: #4c5cec;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        margin: 5px;
      }

      .button:hover {
        background: #3a4bc8;
      }

      .status {
        padding: 10px;
        border-radius: 5px;
        margin: 10px 0;
      }

      .status.success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }

      .status.error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }

      .highlight {
        background: #e7f3ff;
        border: 1px solid #b3d9ff;
        padding: 15px;
        border-radius: 5px;
        margin: 15px 0;
      }

      .highlight strong {
        color: #0066cc;
      }

      .param-table {
        width: 100%;
        border-collapse: collapse;
        margin: 15px 0;
      }

      .param-table th,
      .param-table td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: left;
      }

      .param-table th {
        background-color: #f2f2f2;
        font-weight: bold;
      }

      .param-table code {
        background: #f8f8f8;
        padding: 2px 4px;
        border-radius: 3px;
        font-size: 12px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>EaseAI Widget - 基础演示</h1>

      <div class="highlight">
        <strong>新功能：</strong>
        现在支持自定义客户端ID！通过 <code>data-client-id</code> 参数可以指定不同的客户端标识。
      </div>

      <div class="section">
        <h2>Widget状态</h2>
        <div id="status" class="status error">Widget未加载</div>
      </div>

      <div class="section">
        <h2>配置参数说明</h2>
        <table class="param-table">
          <thead>
            <tr>
              <th>参数</th>
              <th>说明</th>
              <th>必填</th>
              <th>默认值</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><code>data-agent-id</code></td>
              <td>对外发布的Agent ID</td>
              <td>是</td>
              <td>-</td>
            </tr>
            <tr>
              <td><code>data-client-id</code></td>
              <td>客户端标识ID</td>
              <td>否</td>
              <td>e5cd7e4891bf95d1d19206ce24a7b32e</td>
            </tr>
            <tr>
              <td><code>data-api-endpoint</code></td>
              <td>API服务地址</td>
              <td>否</td>
              <td>/prod-api</td>
            </tr>
            <tr>
              <td><code>data-static-url</code></td>
              <td>静态资源URL前缀</td>
              <td>否</td>
              <td>-</td>
            </tr>
            <tr>
              <td><code>data-password</code></td>
              <td>对外发布访问密码</td>
              <td>否</td>
              <td>-</td>
            </tr>
            <tr>
              <td><code>data-default-message</code></td>
              <td>快捷消息，用逗号分割多个消息，显示在发送框上方</td>
              <td>否</td>
              <td>-</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="section">
        <h2>基础配置示例</h2>
        <div class="code-block">
          <code>
            &lt;script src="../dist-widget/easeai-widget.js" data-agent-id="1950443780227665922"
            data-client-id="e5cd7e4891bf95d1d19206ce24a7b32e" data-title="智能客服助手"
            data-api-endpoint="http://demo.easeidea.com/prod-api"
            data-static-url="http://demo.easeidea.com"&gt; &lt;/script&gt;
          </code>
        </div>
      </div>

      <div class="section">
        <h2>测试功能</h2>
        <button class="button" onclick="testWidget()">显示Widget</button>
        <button class="button" onclick="hideWidget()">隐藏Widget</button>
        <button class="button" onclick="checkStatus()">检查状态</button>
        <button class="button" onclick="showConfig()">显示配置</button>
      </div>

      <div class="section">
        <h2>当前配置</h2>
        <div id="config-info" class="code-block">
          <code>配置信息将在点击"显示配置"后显示</code>
        </div>
      </div>
    </div>

    <!-- 加载 EaseAI Widget -->
    <script
      src="../dist-widget/easeai-widget.js"
      data-agent-id="1950443780227665922"
      data-client-id="e5cd7e4891bf95d1d19206ce24a7b32e"
      data-title="智能客服助手"
      data-subtitle="我是您的专属AI助手，随时为您提供帮助"
      data-welcome-message="您好！有什么可以帮助您的吗？"
      data-input-placeholder="请输入您的问题..."
      data-position="bottom-right"
      data-theme="light"
      data-primary-color="#4c5cec"
      data-api-endpoint="http://demo.easeidea.com/prod-api"
      data-static-url="http://demo.easeidea.com"
      data-default-message="介绍一下你自己,今天天气怎么样,推荐一本好书"
    ></script>

    <script>
      // 检查Widget状态
      function checkStatus() {
        const statusEl = document.getElementById('status')
        if (window.EaseAI) {
          statusEl.className = 'status success'
          statusEl.textContent = 'Widget已成功加载'
        } else {
          statusEl.className = 'status error'
          statusEl.textContent = 'Widget未加载'
        }
      }

      // 显示Widget
      function testWidget() {
        if (window.EaseAI) {
          window.EaseAI.show()
          console.log('Widget已显示')
        } else {
          console.log('Widget未加载')
        }
      }

      // 隐藏Widget
      function hideWidget() {
        if (window.EaseAI) {
          window.EaseAI.hide()
          console.log('Widget已隐藏')
        } else {
          console.log('Widget未加载')
        }
      }

      // 显示配置信息
      function showConfig() {
        const configEl = document.getElementById('config-info')
        const script = document.querySelector('script[src*="easeai-widget"]')

        if (script) {
          const config = {
            agentId: script.getAttribute('data-agent-id'),
            clientId: script.getAttribute('data-client-id'),
            title: script.getAttribute('data-title'),
            subtitle: script.getAttribute('data-subtitle'),
            welcomeMessage: script.getAttribute('data-welcome-message'),
            inputPlaceholder: script.getAttribute('data-input-placeholder'),
            position: script.getAttribute('data-position'),
            theme: script.getAttribute('data-theme'),
            primaryColor: script.getAttribute('data-primary-color'),
            apiEndpoint: script.getAttribute('data-api-endpoint'),
            staticUrl: script.getAttribute('data-static-url'),
          }

          configEl.innerHTML = `<code>${JSON.stringify(config, null, 2)}</code>`
        }
      }

      // 页面加载完成后检查状态
      window.addEventListener('load', () => {
        setTimeout(checkStatus, 1000)
      })
    </script>
  </body>
</html>
