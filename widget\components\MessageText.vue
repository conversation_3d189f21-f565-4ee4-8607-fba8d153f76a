<script lang="ts" setup>
import { computed, onMounted, onUnmounted, onUpdated, ref, watch } from 'vue'
import MarkdownIt from 'markdown-it'
import MdKatex from '@vscode/markdown-it-katex'
import MdLinkAttributes from 'markdown-it-link-attributes'
import hljs from 'highlight.js'

interface Props {
  inversion?: boolean
  error?: boolean
  text?: string
  loading?: boolean
  asRawText?: boolean
}

const props = defineProps<Props>()

// Refs
const textRef = ref<HTMLElement>()
const displayedText = ref('')
const fullText = ref('')
const isTyping = ref(false)

// Typing effect variables
const typingSpeed = 15
const typingBatchSize = 3
let typingTimer: number | null = null

// Markdown-it configuration
const mdBaseConfig = {
  html: false,
  linkify: true,
}

const mdi = new MarkdownIt({
  ...mdBaseConfig,
  highlight(code, language) {
    const validLang = !!(language && hljs.getLanguage(language))
    if (validLang) {
      const lang = language ?? ''
      return highlightBlock(hljs.highlight(code, { language: lang }).value, lang)
    }
    return highlightBlock(hljs.highlightAuto(code).value, '')
  },
})

// Add plugins
mdi.use(MdLinkAttributes, { attrs: { target: '_blank', rel: 'noopener' } })
mdi.use(MdKatex, {})

/**
 * 渲染 Markdown 文本为 HTML
 */
const renderMarkdown = (text: string) => {
  return mdi.render(text)
}

/**
 * 处理文本内容并转换为 HTML
 */
const processTextToHtml = (value: string, asRawText: boolean) => {
  if (asRawText) return value
  return renderMarkdown(value)
}

const html = computed(() => {
  const value = props.text ?? ''
  if (props.inversion || props.asRawText) {
    return value
  }
  const textToRender = !isTyping.value || props.loading === false ? value : displayedText.value
  return processTextToHtml(textToRender, false)
})

/**
 * 生成代码块的 HTML，包含语言标识和复制按钮
 */
function highlightBlock(str: string, lang?: string) {
  return `<pre class="code-block-wrapper"><div class="code-block-header"><span class="code-block-header__lang">${lang}</span><span class="code-block-header__copy cursor-pointer">复制代码</span></div><code class="hljs code-block-body ${lang}">${str}</code></pre>`
}

// Computed properties
const wrapClass = computed(() => {
  return ['widget-message-text', { 'widget-message-error': props.error }]
})

/**
 * 实现打字机效果
 */
function startTypewriterEffect(startText: string, targetText: string) {
  if (typingTimer) {
    clearTimeout(typingTimer)
    typingTimer = null
  }
  isTyping.value = true
  displayedText.value = startText
  if (displayedText.value === targetText) {
    isTyping.value = false
    return
  }
  let currentIndex = displayedText.value.length
  const typeNextChar = () => {
    if (currentIndex < targetText.length) {
      const endIndex = Math.min(currentIndex + typingBatchSize, targetText.length)
      displayedText.value = targetText.substring(0, endIndex)
      currentIndex = endIndex
      const hasSpecialChars = /[`<|]/.test(
        targetText.substring(Math.max(0, currentIndex - 20), currentIndex),
      )
      typingTimer = window.setTimeout(typeNextChar, hasSpecialChars ? typingSpeed / 2 : typingSpeed)
    } else {
      isTyping.value = false
    }
  }
  typingTimer = window.setTimeout(typeNextChar, typingSpeed)
}

/**
 * 清理资源
 */
function cleanup() {
  if (typingTimer) {
    clearTimeout(typingTimer)
    typingTimer = null
  }
}

/**
 * 复制文本到剪贴板
 */
async function copyToClipboard(text: string) {
  try {
    await navigator.clipboard.writeText(text)
    return true
  } catch (err) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = text
    document.body.appendChild(textArea)
    textArea.select()
    const success = document.execCommand('copy')
    document.body.removeChild(textArea)
    return success
  }
}

/**
 * 为代码块的复制按钮添加事件监听
 */
function addCopyEvents() {
  if (textRef.value) {
    const copyBtn = textRef.value.querySelectorAll('.code-block-header__copy')
    copyBtn.forEach(btn => {
      btn.addEventListener('click', async () => {
        const code = btn.parentElement?.nextElementSibling?.textContent
        if (code) {
          const success = await copyToClipboard(code)
          if (success) {
            btn.textContent = '已复制'
            setTimeout(() => {
              btn.textContent = '复制代码'
            }, 1000)
          }
        }
      })
    })
  }
}

watch(
  () => props.text,
  newText => {
    const newTextValue = newText || ''
    if (newTextValue && !props.inversion) {
      // Widget 中简化打字机效果，直接显示
      displayedText.value = newTextValue
      fullText.value = newTextValue
      isTyping.value = false
    } else {
      displayedText.value = newTextValue
      fullText.value = newTextValue
      isTyping.value = false
    }
  },
  { immediate: true },
)

watch(
  () => props.loading,
  newLoading => {
    if (newLoading === false && fullText.value) {
      displayedText.value = fullText.value
      isTyping.value = false
      if (typingTimer) {
        clearTimeout(typingTimer)
        typingTimer = null
      }
    }
  },
  { immediate: true },
)

// Lifecycle hooks
onMounted(() => {
  addCopyEvents()
})

onUpdated(() => {
  addCopyEvents()
})

onUnmounted(() => {
  cleanup()
})
</script>

<template>
  <div :class="wrapClass">
    <div ref="textRef" class="widget-message-content">
      <!-- 用户消息 -->
      <div v-if="inversion" class="widget-user-text" v-text="html" />
      <!-- AI回复 -->
      <div v-else class="widget-ai-text">
        <div
          v-if="!asRawText"
          class="markdown-body"
          :class="{ 'markdown-body-generate': loading }"
          v-html="html"
        />
        <div v-else class="whitespace-pre-wrap" v-text="html" />
      </div>
    </div>
  </div>
</template>

<style scoped>
.widget-message-text {
  width: 100%;
}

.widget-message-content {
  word-wrap: break-word;
  word-break: break-word;
  line-height: 1.5;
}

.widget-user-text {
  white-space: pre-wrap;
  font-size: 14px;
}

.widget-ai-text {
  font-size: 14px;
}

.widget-message-error {
  color: #ef4444;
}

/* Markdown 样式 */
:deep(.markdown-body) {
  font-size: 14px;
  line-height: 1.6;
}

:deep(.markdown-body h1),
:deep(.markdown-body h2),
:deep(.markdown-body h3),
:deep(.markdown-body h4),
:deep(.markdown-body h5),
:deep(.markdown-body h6) {
  margin-top: 1em;
  margin-bottom: 0.5em;
  font-weight: 600;
}

:deep(.markdown-body p) {
  margin-bottom: 1em;
}

:deep(.markdown-body ul),
:deep(.markdown-body ol) {
  margin-bottom: 1em;
  padding-left: 1.5em;
}

:deep(.markdown-body li) {
  margin-bottom: 0.25em;
}

:deep(.markdown-body blockquote) {
  border-left: 4px solid #e5e7eb;
  padding-left: 1em;
  margin: 1em 0;
  color: #6b7280;
}

:deep(.markdown-body table) {
  border-collapse: collapse;
  width: 100%;
  margin: 1em 0;
}

:deep(.markdown-body th),
:deep(.markdown-body td) {
  border: 1px solid #e5e7eb;
  padding: 0.5em;
  text-align: left;
}

:deep(.markdown-body th) {
  background-color: #f9fafb;
  font-weight: 600;
}

/* 代码块样式 */
:deep(.code-block-wrapper) {
  margin: 1em 0;
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

:deep(.code-block-header) {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5em 1em;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e5e7eb;
  font-size: 12px;
}

:deep(.code-block-header__lang) {
  color: #6b7280;
  font-weight: 500;
}

:deep(.code-block-header__copy) {
  color: #4c5cec;
  cursor: pointer;
  font-size: 12px;
}

:deep(.code-block-header__copy:hover) {
  color: #3730a3;
}

:deep(.code-block-body) {
  display: block;
  padding: 1em;
  background-color: #f8f9fa;
  overflow-x: auto;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
}

/* 行内代码样式 */
:deep(.markdown-body code:not(.hljs)) {
  background-color: #f1f5f9;
  padding: 0.125em 0.25em;
  border-radius: 3px;
  font-size: 0.875em;
  font-family: 'Courier New', monospace;
}

/* 链接样式 */
:deep(.markdown-body a) {
  color: #4c5cec;
  text-decoration: none;
}

:deep(.markdown-body a:hover) {
  text-decoration: underline;
}

/* 加载状态动画 */
.markdown-body-generate {
  position: relative;
}

// todo 这个黑色颜色合适吗
.markdown-body-generate::after {
  content: '';
  display: inline-block;
  width: 2px;
  height: 16px;
  background-color: #999;
  animation: blink 1s infinite;
  margin-left: 2px;
}

@keyframes blink {
  0%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0;
  }
}
</style>
